# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

import torch
import torch.nn as nn
from huggingface_hub import PyTorchModelHubMixin  # used for model hub

from vggt.models.aggregator import Aggregator
from vggt.heads.camera_head import CameraHead
from vggt.heads.dpt_head import DPTHead
import torch.nn.functional as F
# from vggt.heads.track_head import TrackHead  # 注释掉未使用的track_head


class VGGT(nn.Module, PyTorchModelHubMixin):
    def __init__(self, img_size=512, patch_size=14, embed_dim=1024):
        super().__init__()

        self.aggregator = Aggregator(img_size=int(img_size*14/16), patch_size=patch_size, embed_dim=embed_dim)
      
        # Multi-scale feature head for generating feature pyramid
        self.feature_head = DPTHead(
            dim_in=2 * embed_dim,
            patch_size=patch_size,
            features=256,
            pos_embed=True
        )

    def forward(
        self,
        images: torch.Tensor,
    ):
        """
        Forward pass of the VGGT model.

        Args:
            images (torch.Tensor): Input images with shape [S, 3, H, W] or [B, S, 3, H, W], in range [0, 1].
                B: batch size, S: sequence length, 3: RGB channels, H: height, W: width
            # query_points (torch.Tensor, optional): Query points for tracking, in pixel coordinates.
            #     Shape: [N, 2] or [B, N, 2], where N is the number of query points.
            #     Default: None  # 注释掉未使用的参数说明

        Returns:
            dict: A dictionary containing the following predictions:
                - depth (torch.Tensor): Predicted depth maps with shape [B, S, 1, H, W]
                - depth_conf (torch.Tensor): Confidence scores for depth predictions with shape [B, S, 1, H, W]
                - feat_1_4 (torch.Tensor): Multi-scale features at 1/4 resolution with shape [B, S, 256, H/4, W/4]
                - feat_1_8 (torch.Tensor): Multi-scale features at 1/8 resolution with shape [B, S, 256, H/8, W/8]
                - feat_1_16 (torch.Tensor): Multi-scale features at 1/16 resolution with shape [B, S, 256, H/16, W/16]
                - feat_1_32 (torch.Tensor): Multi-scale features at 1/32 resolution with shape [B, S, 256, H/32, W/32]
                - images (torch.Tensor): Original input images, preserved for visualization

                # If query_points is provided, also includes:  # 注释掉未使用的返回值说明
                # - conf (torch.Tensor): Confidence scores for tracked points with shape [B, S, N]
        """

        # If without batch dimension, add it
        # 输入: images [S, 3, H, W] 或 [B, S, 3, H, W] -> 输出: images [B, S, 3, H, W]
        if len(images.shape) == 4:
            images = images.unsqueeze(0)
        
        # 统一的图像预处理
        resize_factor = 14 / 16
        # 保存原始形状
        B, S, C, H, W = images.shape
        # 重塑为 [B*S, C, H, W] 以便进行插值
        images = images.view(B * S, C, H, W)
        # 进行插值
        images = F.interpolate(images, scale_factor=resize_factor, mode='bilinear', align_corners=True)
        # 重新 reshape 回 [B, S, C, H', W']
        _, _, H_new, W_new = images.shape
        images = images.view(B, S, C, H_new, W_new)
       
        # 调用Aggregator处理图像，提取多尺度特征
        # aggregated_tokens_list: List[torch.Tensor]，每个元素形状为 [B, S*P, 2*C]
        # patch_start_idx: int，patch tokens在token序列中的起始索引
        aggregated_tokens_list, patch_start_idx = self.aggregator(images)

        predictions = {}
        multi_scale_features = self.feature_head(
            aggregated_tokens_list, images=images, patch_start_idx=patch_start_idx
        )
        predictions["feat_1_4"] = multi_scale_features[0]
        predictions["feat_1_8"] = multi_scale_features[1]
        predictions["feat_1_16"] = multi_scale_features[2]
        predictions["feat_1_32"] = multi_scale_features[3]

        predictions["images"] = images

        return predictions