#!/usr/bin/env python3
"""
简化的VGGT模型测试脚本
根据修改后的vggt.py生成，只测试基本的导入和推理功能
"""

import os
import sys
import torch

# 设置路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, 'vggt'))

def load_model_weights(model, weight_path):
    """
    加载模型权重并验证参数匹配
    
    规则:
    1. 权重文件中存在但模型中不存在的参数 - 正常，忽略
    2. 模型中存在但权重文件中不存在的参数 - 报错
    3. 都存在的参数但维度不匹配 - 报错
    """
    print(f"📥 加载权重文件: {weight_path}")
    
    if not os.path.exists(weight_path):
        raise FileNotFoundError(f"权重文件不存在: {weight_path}")
    
    # 加载权重
    checkpoint = torch.load(weight_path, map_location='cpu')
    state_dict = checkpoint if isinstance(checkpoint, dict) else checkpoint['state_dict']
    
    model_dict = model.state_dict()
    loaded_params = {}
    mismatched_params = []
    
    # 检查权重文件中的参数
    for name, param in state_dict.items():
        if name in model_dict:
            # 参数在模型中存在，检查维度是否匹配
            if param.shape == model_dict[name].shape:
                loaded_params[name] = param
            else:
                mismatched_params.append((name, param.shape, model_dict[name].shape))
    
    # 检查模型中是否存在权重文件中没有的参数
    missing_params = []
    for name in model_dict.keys():
        if name not in state_dict:
            missing_params.append(name)
    
    # 报告检查结果
    print(f"📊 权重文件参数总数: {len(state_dict)}")
    print(f"📊 模型参数总数: {len(model_dict)}")
    print(f"✅ 成功加载参数: {len(loaded_params)}")
    
    if mismatched_params:
        print("❌ 维度不匹配的参数:")
        for name, weight_shape, model_shape in mismatched_params[:5]:  # 只显示前5个
            print(f"   {name}: 权重文件 {weight_shape} vs 模型 {model_shape}")
        raise ValueError(f"发现 {len(mismatched_params)} 个参数维度不匹配")
    
    if missing_params:
        print("❌ 模型中存在但权重文件中缺失的参数:")
        for name in missing_params[:10]:  # 只显示前10个
            print(f"   {name}")
        raise ValueError(f"权重文件缺失 {len(missing_params)} 个模型必需的参数")
    
    # 加载匹配的参数
    model.load_state_dict(loaded_params, strict=False)
    print("✅ 权重加载完成")

def main():
    print("🔧 VGGT模型简单测试")
    print("=" * 40)
    
    # 1. 环境信息
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"设备: {device}")
    print(f"PyTorch: {torch.__version__}")
    
    # 2. 导入模型
    print("\n📦 导入模型...")
    try:
        sys.path.append('./vggt/vggt')
        from models.vggt import VGGT
        print("✓ VGGT模型导入成功")
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return
    
    # 3. 创建模型
    print("\n🏗️ 创建模型...")
    try:
        model = VGGT()
        model = model.to(device)
        model.eval()
        print("✓ 模型创建成功")
        
        # 统计参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"✓ 参数数量: {total_params:,}")
        
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
        return
    
    # 4. 加载权重
    print("\n📂 加载权重...")
    try:
        weight_path = "vggt/checkpoint/model.pt"
        load_model_weights(model, weight_path)
        print("✓ 权重加载成功")
    except Exception as e:
        print(f"⚠️ 权重加载失败: {e}")
        print("⚠️ 继续使用随机初始化的权重进行测试...")
        # 不返回，继续测试
    
    # 5. 创建随机输入
    print("\n📊 准备输入数据...")
    try:
        # 创建随机输入 (批次=1, 序列=2, 通道=3, 高度=518, 宽度=518)
        batch_size = 1
        sequence_length = 2  # 立体对
        height, width = 512, 512
        
        images = torch.randn(batch_size, sequence_length, 3, height, width).to(device)
        print(f"✓ 输入图像: {images.shape}")
        print(f"✓ 输入范围: [{images.min():.3f}, {images.max():.3f}]")
        
    except Exception as e:
        print(f"✗ 输入创建失败: {e}")
        return
    
    # 6. 模型推理
    print("\n🚀 开始推理...")
    try:
        with torch.no_grad():
            outputs = model(images)
        
        print("✓ 推理成功")
        
    except Exception as e:
        print(f"✗ 推理失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 7. 打印输出维度
    print("\n📋 输出结果:")
    print("-" * 30)
    
    for key, value in outputs.items():
        if hasattr(value, 'shape'):
            print(f"{key:15s}: {str(value.shape):25s} | 数据类型: {value.dtype}")
            
            # 打印数值范围
            if hasattr(value, 'min') and hasattr(value, 'max'):
                try:
                    min_val = value.min().item()
                    max_val = value.max().item()
                    print(f"{'':15s}  范围: [{min_val:.4f}, {max_val:.4f}]")
                except:
                    pass
        else:
            print(f"{key:15s}: {type(value):25s}")
        print()
    
    # 8. 验证输出
    print("✅ 验证结果:")
    expected_outputs = ['depth', 'depth_conf', 'feat_1_4', 'feat_1_8', 'feat_1_16', 'feat_1_32', 'images']

    for expected in expected_outputs:
        if expected in outputs:
            print(f"✓ {expected} - 存在")
        else:
            print(f"✗ {expected} - 缺失")

    # 9. 验证多尺度特征的分辨率
    print("\n🔍 多尺度特征验证:")
    print("-" * 30)

    if 'feat_1_4' in outputs and 'feat_1_8' in outputs and 'feat_1_16' in outputs and 'feat_1_32' in outputs:
        feat_1_4 = outputs['feat_1_4']
        feat_1_8 = outputs['feat_1_8']
        feat_1_16 = outputs['feat_1_16']
        feat_1_32 = outputs['feat_1_32']

        # 验证特征尺寸关系
        expected_h_4, expected_w_4 = height // 4, width // 4
        expected_h_8, expected_w_8 = height // 8, width // 8
        expected_h_16, expected_w_16 = height // 16, width // 16
        expected_h_32, expected_w_32 = height // 32, width // 32

        print(f"原始图像尺寸: {height} x {width}")
        print(f"feat_1_4  期望: [B, S, 256, {expected_h_4}, {expected_w_4}]  实际: {list(feat_1_4.shape)}")
        print(f"feat_1_8  期望: [B, S, 256, {expected_h_8}, {expected_w_8}]  实际: {list(feat_1_8.shape)}")
        print(f"feat_1_16 期望: [B, S, 256, {expected_h_16}, {expected_w_16}]  实际: {list(feat_1_16.shape)}")
        print(f"feat_1_32 期望: [B, S, 256, {expected_h_32}, {expected_w_32}]  实际: {list(feat_1_32.shape)}")

        # 验证尺寸是否正确
        size_checks = [
            (feat_1_4.shape[-2:], (expected_h_4, expected_w_4), "1/4"),
            (feat_1_8.shape[-2:], (expected_h_8, expected_w_8), "1/8"),
            (feat_1_16.shape[-2:], (expected_h_16, expected_w_16), "1/16"),
            (feat_1_32.shape[-2:], (expected_h_32, expected_w_32), "1/32"),
        ]

        all_correct = True
        for actual_size, expected_size, scale_name in size_checks:
            if actual_size == expected_size:
                print(f"✓ {scale_name} 尺度特征尺寸正确")
            else:
                print(f"✗ {scale_name} 尺度特征尺寸错误: 期望 {expected_size}, 实际 {actual_size}")
                all_correct = False

        if all_correct:
            print("\n🎉 所有多尺度特征尺寸验证通过!")
        else:
            print("\n❌ 部分多尺度特征尺寸验证失败!")
    else:
        print("❌ 缺少多尺度特征输出，无法验证尺寸")
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        import traceback
        traceback.print_exc()